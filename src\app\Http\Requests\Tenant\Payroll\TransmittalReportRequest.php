<?php

namespace App\Http\Requests\Tenant\Payroll;

use App\Http\Requests\BaseRequest;

class TransmittalReportRequest extends BaseRequest
{
    public function rules(): array
    {
        return [
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
            'department_id' => 'nullable|exists:departments,id',
            'payrun_id' => 'nullable|exists:payruns,id',
            'prepared_by' => 'nullable|string|max:255'
        ];
    }

    public function messages(): array
    {
        return [
            'start_date.required' => __t('start_date_is_required'),
            'start_date.date' => __t('start_date_must_be_valid_date'),
            'end_date.required' => __t('end_date_is_required'),
            'end_date.date' => __t('end_date_must_be_valid_date'),
            'end_date.after_or_equal' => __t('end_date_must_be_after_or_equal_start_date'),
            'department_id.exists' => __t('selected_department_is_invalid'),
            'payrun_id.exists' => __t('selected_payrun_is_invalid'),
            'prepared_by.string' => __t('prepared_by_must_be_string'),
            'prepared_by.max' => __t('prepared_by_may_not_be_greater_than_255_characters')
        ];
    }
}
