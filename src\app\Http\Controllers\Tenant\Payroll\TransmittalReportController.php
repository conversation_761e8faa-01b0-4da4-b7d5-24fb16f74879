<?php

namespace App\Http\Controllers\Tenant\Payroll;

use App\Filters\Tenant\PayslipFilter;
use App\Helpers\Traits\DateRangeHelper;
use App\Helpers\Traits\SettingKeyHelper;
use App\Helpers\Traits\TenantAble;
use App\Http\Controllers\Controller;
use App\Http\Requests\Tenant\Payroll\TransmittalReportRequest;
use App\Services\Tenant\Payroll\TransmittalReportService;
use App\Services\Tenant\Setting\SettingService as TenantSettingService;
use Barryvdh\DomPDF\Facade\Pdf;

class TransmittalReportController extends Controller
{
    use TenantAble, DateRangeHelper, SettingKeyHelper;

    protected TransmittalReportService $service;
    protected PayslipFilter $filter;

    public function __construct(TransmittalReportService $service, PayslipFilter $filter)
    {
        $this->service = $service;
        $this->filter = $filter;
    }

    public function index(TransmittalReportRequest $request)
    {
        $data = $this->service
            ->filters($this->filter)
            ->prepareTransmittalReportData($request->validated());

        return response()->json($data);
    }

    public function generatePdf(TransmittalReportRequest $request)
    {
        $data = $this->service
            ->filters($this->filter)
            ->prepareTransmittalReportData($request->validated());

        $settings = (object)resolve(TenantSettingService::class)
            ->getFormattedTenantSettings();

        $pdf = PDF::loadView('tenant.payroll.pdf.transmittal', compact('data', 'settings'));

        $filename = 'transmittal-report-' . date('Y-m-d') . '.pdf';

        return $pdf->download($filename);
    }

    public function exportExcel(TransmittalReportRequest $request)
    {
        $data = $this->service
            ->filters($this->filter)
            ->prepareTransmittalReportData($request->validated());

        $filename = 'transmittal-report-' . date('Y-m-d') . '.xlsx';

        return (new \App\Export\TransmittalReportExport($data))->download($filename);
    }
}
