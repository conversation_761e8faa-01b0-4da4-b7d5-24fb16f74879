<?php

namespace App\Http\Controllers\Tenant\Payroll;

use App\Filters\Tenant\PayslipFilter;
use App\Helpers\Traits\DateRangeHelper;
use App\Helpers\Traits\SettingKeyHelper;
use App\Helpers\Traits\TenantAble;
use App\Http\Controllers\Controller;
use App\Http\Requests\Tenant\Payroll\TransmittalReportRequest;
use App\Services\Tenant\Payroll\TransmittalReportService;
use App\Services\Tenant\Setting\SettingService as TenantSettingService;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Support\Facades\Log;

class TransmittalReportController extends Controller
{
    use TenantAble, DateRangeHelper, SettingKeyHelper;

    protected TransmittalReportService $service;
    protected PayslipFilter $filter;

    public function __construct(TransmittalReportService $service, PayslipFilter $filter)
    {
        $this->service = $service;
        $this->filter = $filter;
    }

    public function index(TransmittalReportRequest $request)
    {
        try {
            $data = $this->service
                ->filters($this->filter)
                ->prepareTransmittalReportData($request->validated());

            return response()->json($data);
        } catch (\Exception $e) {
            Log::error('Transmittal Report Error: ' . $e->getMessage());
            return response()->json([
                'error' => 'An error occurred while generating the report',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    public function generatePdf(TransmittalReportRequest $request)
    {
        try {
            $data = $this->service
                ->filters($this->filter)
                ->prepareTransmittalReportData($request->validated());

            $settings = (object)resolve(TenantSettingService::class)
                ->getFormattedTenantSettings();

            $pdf = PDF::loadView('tenant.payroll.pdf.transmittal', compact('data', 'settings'));

            $filename = 'transmittal-report-' . date('Y-m-d') . '.pdf';

            return $pdf->download($filename);
        } catch (\Exception $e) {
            Log::error('Transmittal PDF Error: ' . $e->getMessage());
            return response()->json([
                'error' => 'An error occurred while generating the PDF',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    public function exportExcel(TransmittalReportRequest $request)
    {
        try {
            $data = $this->service
                ->filters($this->filter)
                ->prepareTransmittalReportData($request->validated());

            $filename = 'transmittal-report-' . date('Y-m-d') . '.xlsx';

            return (new \App\Export\TransmittalReportExport($data))->download($filename);
        } catch (\Exception $e) {
            Log::error('Transmittal Excel Error: ' . $e->getMessage());
            return response()->json([
                'error' => 'An error occurred while generating the Excel file',
                'message' => $e->getMessage()
            ], 500);
        }
    }
}
