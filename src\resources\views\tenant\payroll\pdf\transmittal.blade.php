<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>Transmittal Report</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            margin: 20px;
            line-height: 1.4;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .header h1 {
            font-size: 18px;
            font-weight: bold;
            margin: 0;
            text-decoration: underline;
        }
        .header h2 {
            font-size: 14px;
            margin: 5px 0;
        }
        .table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        .table th, .table td {
            border: 1px solid #000;
            padding: 8px;
            text-align: left;
        }
        .table th {
            background-color: #f5f5f5;
            font-weight: bold;
            text-align: center;
        }
        .table .number-col {
            width: 15%;
            text-align: center;
        }
        .table .name-col {
            width: 40%;
        }
        .table .designation-col {
            width: 30%;
        }
        .table .salary-col {
            width: 15%;
            text-align: right;
        }
        .total-row {
            font-weight: bold;
            background-color: #f0f0f0;
        }
        .footer {
            margin-top: 40px;
        }
        .prepared-by {
            margin-top: 30px;
        }
        .prepared-by p {
            margin: 2px 0;
        }
        .department-header {
            background-color: #e0e0e0;
            font-weight: bold;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>{{ $data['title'] }}</h1>
        <h2>{{ $data['subtitle'] }}</h2>
    </div>

    <table class="table">
        <thead>
            <tr>
                <th class="number-col">EMPLOYEE NUMBER</th>
                <th class="name-col">NAME</th>
                <th class="designation-col">DESIGNATION</th>
                <th class="salary-col">TOTAL SALARY</th>
            </tr>
        </thead>
        <tbody>
            @foreach($data['departments'] as $department)
                @if(count($data['departments']) > 1)
                    <tr class="department-header">
                        <td colspan="4">{{ $department['department'] }}</td>
                    </tr>
                @endif
                
                @foreach($department['employees'] as $employee)
                    <tr>
                        <td class="number-col">{{ $employee['number'] }}</td>
                        <td class="name-col">{{ $employee['name'] }}</td>
                        <td class="designation-col">{{ $employee['designation'] }}</td>
                        <td class="salary-col">{{ $employee['total_salary'] }}</td>
                    </tr>
                @endforeach
                
                @if(count($data['departments']) > 1)
                    <tr class="total-row">
                        <td colspan="3" style="text-align: right;"><strong>Department Total:</strong></td>
                        <td class="salary-col"><strong>{{ number_format($department['department_total'], 2) }}</strong></td>
                    </tr>
                @endif
            @endforeach
            
            <tr class="total-row">
                <td colspan="3" style="text-align: right;"><strong>TOTAL</strong></td>
                <td class="salary-col"><strong>{{ number_format($data['total_salary'], 2) }}</strong></td>
            </tr>
        </tbody>
    </table>

    <div class="footer">
        <div class="prepared-by">
            <p><strong>Prepared By:</strong></p>
            <br>
            <br>
            <p><strong>{{ $data['prepared_by'] }}</strong></p>
            <p>{{ $data['prepared_title'] }}</p>
        </div>
    </div>
</body>
</html>
