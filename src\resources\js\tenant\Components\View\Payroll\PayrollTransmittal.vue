<template>
    <div class="content-wrapper">
        <app-page-top-section :title="$t('transmittal')">
            <app-default-button
                :title="$fieldTitle('add', 'transmittal', true)"
                v-if="$can('create_payroll_transmittal')"
                @click="openModal()"
            />
        </app-page-top-section>

        <div class="card border-0">
            <div class="card-header d-flex align-items-center justify-content-between primary-card-color">
                <h5 class="card-title mb-0">{{ $t('transmittal_list') }}</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-12">
                        <p class="text-muted">{{ $t('transmittal_description') }}</p>
                        <!-- Add your transmittal content here -->
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle mr-2"></i>
                            {{ $t('transmittal_coming_soon') }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: "PayrollTransmittal",
    data() {
        return {
            isModalActive: false,
            loading: false,
        }
    },
    methods: {
        openModal() {
            this.isModalActive = true;
        },
    }
}
</script>

<style scoped>
.content-wrapper {
    padding: 20px;
}
</style>
