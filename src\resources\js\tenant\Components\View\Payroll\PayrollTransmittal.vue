<template>
    <div class="content-wrapper">
        <app-page-top-section :title="$t('transmittal')">
            <app-default-button
                :title="$t('generate_report')"
                v-if="$can('create_payroll_transmittal')"
                @click="generateReport"
                :loading="loading"
            />
        </app-page-top-section>

        <div class="card border-0">
            <div class="card-header d-flex align-items-center justify-content-between primary-card-color">
                <h5 class="card-title mb-0">{{ $t('transmittal_report') }}</h5>
            </div>
            <div class="card-body">
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="form-group">
                            <label>{{ $t('start_date') }} <span class="text-danger">*</span></label>
                            <app-input
                                type="date"
                                v-model="filters.start_date"
                                :placeholder="$t('select_start_date')"
                                :field-props="fieldStatus"
                            />
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label>{{ $t('end_date') }} <span class="text-danger">*</span></label>
                            <app-input
                                type="date"
                                v-model="filters.end_date"
                                :placeholder="$t('select_end_date')"
                                :field-props="fieldStatus"
                            />
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label>{{ $t('department') }}</label>
                            <app-input
                                type="select"
                                v-model="filters.department_id"
                                :list="departments"
                                list-value-field="name"
                                :placeholder="$t('select_department')"
                                :field-props="fieldStatus"
                            />
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label>{{ $t('prepared_by') }}</label>
                            <app-input
                                type="text"
                                v-model="filters.prepared_by"
                                :placeholder="$t('enter_prepared_by')"
                                :field-props="fieldStatus"
                            />
                        </div>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-12">
                        <div class="btn-group" role="group">
                            <button
                                type="button"
                                class="btn btn-primary"
                                @click="previewData"
                                :disabled="!canGenerate"
                            >
                                <i class="fas fa-eye mr-1"></i>
                                {{ $t('preview') }}
                            </button>
                            <button
                                type="button"
                                class="btn btn-danger"
                                @click="downloadPdf"
                                :disabled="!canGenerate"
                            >
                                <i class="fas fa-file-pdf mr-1"></i>
                                {{ $t('download_pdf') }}
                            </button>
                            <button
                                type="button"
                                class="btn btn-success"
                                @click="downloadExcel"
                                :disabled="!canGenerate"
                            >
                                <i class="fas fa-file-excel mr-1"></i>
                                {{ $t('download_excel') }}
                            </button>
                        </div>
                    </div>
                </div>

                <div v-if="reportData" class="row">
                    <div class="col-12">
                        <div v-if="reportData.message" class="alert alert-info">
                            <i class="fas fa-info-circle mr-2"></i>
                            {{ reportData.message }}
                        </div>
                        <div v-else class="table-responsive">
                            <table class="table table-bordered">
                                <thead class="thead-light">
                                    <tr>
                                        <th>{{ $t('employee_number') }}</th>
                                        <th>{{ $t('name') }}</th>
                                        <th>{{ $t('designation') }}</th>
                                        <th>{{ $t('total_salary') }}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <template v-for="department in reportData.departments">
                                        <tr v-if="reportData.departments.length > 1" class="table-secondary" :key="'dept-' + department.department">
                                            <td colspan="4" class="text-center font-weight-bold">
                                                {{ department.department }}
                                            </td>
                                        </tr>
                                        <tr v-for="employee in department.employees" :key="employee.number">
                                            <td>{{ employee.number }}</td>
                                            <td>{{ employee.name }}</td>
                                            <td>{{ employee.designation }}</td>
                                            <td class="text-right">{{ employee.total_salary }}</td>
                                        </tr>
                                    </template>
                                    <tr v-if="reportData.departments.length > 0" class="table-warning font-weight-bold">
                                        <td colspan="3" class="text-right">{{ $t('total') }}:</td>
                                        <td class="text-right">{{ formatCurrency(reportData.total_salary) }}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import {axiosGet} from "../../../../common/Helper/AxiosHelper";
import {FormMixin} from "../../../../core/mixins/form/FormMixin";

export default {
    name: "PayrollTransmittal",
    mixins: [FormMixin],
    data() {
        return {
            loading: false,
            filters: {
                start_date: '',
                end_date: '',
                department_id: '',
                prepared_by: 'Via Mae N. Tembrevilla'
            },
            departments: [],
            reportData: null
        }
    },
    computed: {
        canGenerate() {
            return this.filters.start_date && this.filters.end_date;
        }
    },
    mounted() {
        this.loadDepartments();
    },
    methods: {
        async loadDepartments() {
            try {
                const response = await axiosGet('/selectable/departments');
                this.departments = response.data;
            } catch (error) {
                console.error('Error loading departments:', error);
            }
        },

        async previewData() {
            if (!this.canGenerate) {
                this.$toastr.e('Please select start and end dates');
                return;
            }

            this.loading = true;
            this.reportData = null;

            try {
                const response = await axiosGet('/app/transmittal/data', {
                    params: this.filters
                });

                if (response.data) {
                    this.reportData = response.data;
                    if (response.data.message) {
                        this.$toastr.i(response.data.message);
                    }
                } else {
                    this.$toastr.e('No data received from server');
                }
            } catch (error) {
                console.error('Error loading transmittal data:', error);
                this.$toastr.e(error.response?.data?.message || 'Error loading data. Please try again.');
                this.reportData = null;
            } finally {
                this.loading = false;
            }
        },

        async downloadPdf() {
            if (!this.canGenerate) {
                this.$toastr.e('Please select start and end dates');
                return;
            }

            this.loading = true;
            try {
                const response = await axiosGet('/app/transmittal/pdf', {
                    params: this.filters,
                    responseType: 'blob'
                });

                if (response.data && response.data.size > 0) {
                    const url = window.URL.createObjectURL(new Blob([response.data], { type: 'application/pdf' }));
                    const link = document.createElement('a');
                    link.href = url;
                    link.setAttribute('download', `transmittal-report-${new Date().toISOString().split('T')[0]}.pdf`);
                    document.body.appendChild(link);
                    link.click();
                    link.remove();
                    window.URL.revokeObjectURL(url);
                    this.$toastr.s('PDF downloaded successfully');
                } else {
                    this.$toastr.e('No data to generate PDF');
                }
            } catch (error) {
                console.error('Error generating PDF:', error);
                this.$toastr.e(error.response?.data?.message || 'Error generating PDF. Please try again.');
            } finally {
                this.loading = false;
            }
        },

        async downloadExcel() {
            if (!this.canGenerate) {
                this.$toastr.e('Please select start and end dates');
                return;
            }

            this.loading = true;
            try {
                const response = await axiosGet('/app/transmittal/excel', {
                    params: this.filters,
                    responseType: 'blob'
                });

                if (response.data && response.data.size > 0) {
                    const url = window.URL.createObjectURL(new Blob([response.data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' }));
                    const link = document.createElement('a');
                    link.href = url;
                    link.setAttribute('download', `transmittal-report-${new Date().toISOString().split('T')[0]}.xlsx`);
                    document.body.appendChild(link);
                    link.click();
                    link.remove();
                    window.URL.revokeObjectURL(url);
                    this.$toastr.s('Excel file downloaded successfully');
                } else {
                    this.$toastr.e('No data to generate Excel file');
                }
            } catch (error) {
                console.error('Error generating Excel:', error);
                this.$toastr.e(error.response?.data?.message || 'Error generating Excel file. Please try again.');
            } finally {
                this.loading = false;
            }
        },

        generateReport() {
            this.previewData();
        },

        formatCurrency(amount) {
            return new Intl.NumberFormat('en-US', {
                style: 'decimal',
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            }).format(amount);
        }
    }
}
</script>

<style scoped>
.content-wrapper {
    padding: 20px;
}
</style>
