<?php

namespace App\Export;

use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithTitle;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;

class TransmittalReportExport implements FromArray, WithHeadings, ShouldAutoSize, WithStyles, WithTitle
{
    use Exportable;

    private array $data;

    public function __construct(array $data)
    {
        $this->data = $data;
    }

    public function title(): string
    {
        return 'Transmittal Report';
    }

    public function headings(): array
    {
        return [
            'Employee Number',
            'Name',
            'Designation',
            'Total Salary'
        ];
    }

    public function array(): array
    {
        $rows = [];
        
        // Add title rows
        $rows[] = [$this->data['title'], '', '', ''];
        $rows[] = [$this->data['subtitle'], '', '', ''];
        $rows[] = ['', '', '', '']; // Empty row
        
        // Add header row
        $rows[] = $this->headings();
        
        foreach ($this->data['departments'] as $department) {
            // Add department header if multiple departments
            if (count($this->data['departments']) > 1) {
                $rows[] = [$department['department'], '', '', ''];
            }
            
            // Add employee rows
            foreach ($department['employees'] as $employee) {
                $rows[] = [
                    $employee['number'],
                    $employee['name'],
                    $employee['designation'],
                    $employee['total_salary']
                ];
            }
            
            // Add department total if multiple departments
            if (count($this->data['departments']) > 1) {
                $rows[] = [
                    '',
                    '',
                    'Department Total:',
                    number_format($department['department_total'], 2)
                ];
            }
        }
        
        // Add grand total
        $rows[] = [
            '',
            '',
            'TOTAL:',
            number_format($this->data['total_salary'], 2)
        ];
        
        // Add prepared by section
        $rows[] = ['', '', '', ''];
        $rows[] = ['Prepared By:', '', '', ''];
        $rows[] = ['', '', '', ''];
        $rows[] = [$this->data['prepared_by'], '', '', ''];
        $rows[] = [$this->data['prepared_title'], '', '', ''];
        
        return $rows;
    }

    public function styles(Worksheet $sheet)
    {
        $lastRow = $sheet->getHighestRow();
        
        return [
            // Title styling
            1 => [
                'font' => ['bold' => true, 'size' => 16],
                'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER]
            ],
            2 => [
                'font' => ['bold' => true, 'size' => 12],
                'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER]
            ],
            
            // Header row styling
            4 => [
                'font' => ['bold' => true],
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['rgb' => 'F5F5F5']
                ],
                'borders' => [
                    'allBorders' => [
                        'borderStyle' => Border::BORDER_THIN,
                    ],
                ],
                'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER]
            ],
            
            // Data rows borders
            'A4:D' . ($lastRow - 5) => [
                'borders' => [
                    'allBorders' => [
                        'borderStyle' => Border::BORDER_THIN,
                    ],
                ],
            ],
            
            // Total row styling
            ($lastRow - 5) => [
                'font' => ['bold' => true],
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['rgb' => 'F0F0F0']
                ]
            ]
        ];
    }
}
