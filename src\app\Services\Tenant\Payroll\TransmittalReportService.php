<?php

namespace App\Services\Tenant\Payroll;

use App\Helpers\Traits\DateRangeHelper;
use App\Helpers\Traits\TenantAble;
use App\Models\Tenant\Payroll\Payslip;
use App\Repositories\Core\Status\StatusRepository;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;

class TransmittalReportService
{
    use TenantAble, DateRangeHelper;

    protected $model;
    protected $filter;

    public function __construct()
    {
        $this->model = new Payslip();
    }

    public function filters($filter)
    {
        $this->filter = $filter;
        return $this;
    }

    public function getTransmittalData(array $filters): Collection
    {
        try {
            // Get payroll summary data instead of individual payslips
            return $this->buildTransmittalSummaryQuery($filters);
        } catch (\Exception $e) {
            Log::error('Error in getTransmittalData: ' . $e->getMessage());
            return collect(); // Return empty collection on error
        }
    }

    /**
     * Build transmittal summary query based on payroll summary approach
     */
    protected function buildTransmittalSummaryQuery(array $filters): Collection
    {
        // Get date ranges
        $ranges = $this->getDateRanges($filters);

        // Build base query for payslips with summary data
        $query = $this->model->newQuery()
            ->select([
                'payslips.*',
                'users.first_name',
                'users.last_name',
                'users.email',
                'departments.name as department_name',
                'designations.name as designation_name',
                'payruns.name as payrun_name',
                'payruns.uid as payrun_uid'
            ])
            ->join('users', 'payslips.user_id', '=', 'users.id')
            ->leftJoin('departments', 'users.department_id', '=', 'departments.id')
            ->leftJoin('designations', 'users.designation_id', '=', 'designations.id')
            ->leftJoin('payruns', 'payslips.payrun_id', '=', 'payruns.id');

        // Apply date filters
        if ($ranges) {
            $query->where(function($q) use ($ranges) {
                $q->whereBetween('payslips.start_date', $ranges)
                  ->orWhereBetween('payslips.end_date', $ranges)
                  ->orWhere(function($subQ) use ($ranges) {
                      $subQ->where('payslips.start_date', '<=', $ranges[0])
                           ->where('payslips.end_date', '>=', $ranges[1]);
                  });
            });
        }

        // Apply department filter
        if (isset($filters['department_id']) && !empty($filters['department_id'])) {
            $query->where('users.department_id', $filters['department_id']);
        }

        // Apply payrun filter
        if (isset($filters['payrun_id']) && !empty($filters['payrun_id'])) {
            $query->where('payslips.payrun_id', $filters['payrun_id']);
        }

        // Only include payslips with positive net salary and sent status
        $query->where('payslips.net_salary', '>', 0);

        // Get sent status ID
        $statusRepository = resolve(StatusRepository::class);
        $sentStatusId = $statusRepository->payslipSent();
        if ($sentStatusId) {
            $query->where('payslips.status_id', $sentStatusId);
        }

        return $query->orderBy('departments.name')
                    ->orderBy('users.first_name')
                    ->get()
                    ->map(function ($item) {
                        // Transform the joined data into a more usable format
                        $item->user = (object) [
                            'id' => $item->user_id,
                            'first_name' => $item->first_name,
                            'last_name' => $item->last_name,
                            'email' => $item->email,
                            'full_name' => trim($item->first_name . ' ' . $item->last_name),
                            'department' => (object) [
                                'name' => $item->department_name ?? 'No Department'
                            ],
                            'designation' => (object) [
                                'name' => $item->designation_name ?? 'N/A'
                            ]
                        ];

                        $item->payrun = (object) [
                            'name' => $item->payrun_name,
                            'uid' => $item->payrun_uid
                        ];

                        return $item;
                    });
    }

    /**
     * Get date ranges from filters
     */
    protected function getDateRanges(array $filters): ?array
    {
        if (isset($filters['start_date']) && isset($filters['end_date'])) {
            return [$filters['start_date'], $filters['end_date']];
        }

        return null;
    }

    public function prepareTransmittalReportData(array $filters): array
    {
        $payslipSummary = $this->getTransmittalData($filters);

        // Handle empty results
        if ($payslipSummary->isEmpty()) {
            return [
                'title' => 'ELECTRICAL',
                'subtitle' => 'PAYROLL COVERED AS OF ' . strtoupper(date('M d', strtotime($filters['start_date']))) . '-' . strtoupper(date('M d, Y', strtotime($filters['end_date']))),
                'departments' => [],
                'total_salary' => 0,
                'total_employees' => 0,
                'prepared_by' => $filters['prepared_by'] ?? 'Via Mae N. Tembrevilla',
                'prepared_title' => 'Payroll Analyst',
                'date_range' => [
                    'start' => $filters['start_date'],
                    'end' => $filters['end_date']
                ],
                'message' => 'No payroll summary data found for the selected criteria.'
            ];
        }

        // Group by department and aggregate summary data
        $departmentSummary = $this->aggregatePayrollSummaryByDepartment($payslipSummary);

        $reportData = [];
        $totalSalary = 0;
        $totalEmployees = 0;
        $employeeNumber = 1;

        foreach ($departmentSummary as $departmentName => $departmentData) {
            $departmentTotal = 0;
            $employees = [];

            foreach ($departmentData['employees'] as $employeeData) {
                $employees[] = [
                    'number' => str_pad($employeeNumber, 3, '0', STR_PAD_LEFT) . '-2024',
                    'name' => $employeeData['name'],
                    'designation' => $employeeData['designation'],
                    'total_salary' => number_format($employeeData['total_salary'], 2)
                ];

                $departmentTotal += $employeeData['total_salary'];
                $employeeNumber++;
            }

            $reportData[] = [
                'department' => $departmentName,
                'employees' => $employees,
                'department_total' => $departmentTotal
            ];

            $totalSalary += $departmentTotal;
            $totalEmployees += count($employees);
        }

        return [
            'title' => 'ELECTRICAL',
            'subtitle' => 'PAYROLL COVERED AS OF ' . strtoupper(date('M d', strtotime($filters['start_date']))) . '-' . strtoupper(date('M d, Y', strtotime($filters['end_date']))),
            'departments' => $reportData,
            'total_salary' => $totalSalary,
            'total_employees' => $totalEmployees,
            'prepared_by' => $filters['prepared_by'] ?? 'Via Mae N. Tembrevilla',
            'prepared_title' => 'Payroll Analyst',
            'date_range' => [
                'start' => $filters['start_date'],
                'end' => $filters['end_date']
            ]
        ];
    }

    /**
     * Aggregate payroll summary data by department
     */
    protected function aggregatePayrollSummaryByDepartment(Collection $payslipSummary): array
    {
        $departmentSummary = [];

        foreach ($payslipSummary as $payslip) {
            $departmentName = $payslip->user->department->name ?? 'No Department';
            $employeeId = $payslip->user->id;

            // Initialize department if not exists
            if (!isset($departmentSummary[$departmentName])) {
                $departmentSummary[$departmentName] = [
                    'employees' => [],
                    'total_salary' => 0
                ];
            }

            // Aggregate employee data (sum up multiple payslips for same employee)
            if (!isset($departmentSummary[$departmentName]['employees'][$employeeId])) {
                $departmentSummary[$departmentName]['employees'][$employeeId] = [
                    'name' => $payslip->user->full_name,
                    'designation' => $payslip->user->designation->name ?? 'N/A',
                    'total_salary' => 0,
                    'payslip_count' => 0
                ];
            }

            // Sum up the salary for this employee
            $departmentSummary[$departmentName]['employees'][$employeeId]['total_salary'] += $payslip->net_salary ?? 0;
            $departmentSummary[$departmentName]['employees'][$employeeId]['payslip_count']++;
        }

        // Convert indexed employees array to simple array
        foreach ($departmentSummary as $departmentName => &$departmentData) {
            $departmentData['employees'] = array_values($departmentData['employees']);
        }

        return $departmentSummary;
    }


}
