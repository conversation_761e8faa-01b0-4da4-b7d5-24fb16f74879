<?php

namespace App\Services\Tenant\Payroll;

use App\Helpers\Traits\DateRangeHelper;
use App\Helpers\Traits\TenantAble;
use App\Models\Tenant\Payroll\Payslip;
use App\Services\Tenant\TenantService;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;

class TransmittalReportService extends TenantService
{
    use TenantAble, DateRangeHelper;

    public function __construct(Payslip $payslip)
    {
        $this->model = $payslip;
    }

    public function getTransmittalData(array $filters): Collection
    {
        $query = $this->buildTransmittalQuery($filters);

        return $query->with([
            'user:id,first_name,last_name,email',
            'user.department:id,name',
            'user.designation:id,name',
            'payrun:id,name,uid'
        ])->get();
    }

    public function prepareTransmittalReportData(array $filters): array
    {
        $payslips = $this->getTransmittalData($filters);

        // Handle empty results
        if ($payslips->isEmpty()) {
            return [
                'title' => 'ELECTRICAL',
                'subtitle' => 'PAYROLL COVERED AS OF ' . strtoupper(date('M d', strtotime($filters['start_date']))) . '-' . strtoupper(date('M d, Y', strtotime($filters['end_date']))),
                'departments' => [],
                'total_salary' => 0,
                'total_employees' => 0,
                'prepared_by' => $filters['prepared_by'] ?? 'Via Mae N. Tembrevilla',
                'prepared_title' => 'Payroll Analyst',
                'date_range' => [
                    'start' => $filters['start_date'],
                    'end' => $filters['end_date']
                ],
                'message' => 'No payslip data found for the selected criteria.'
            ];
        }

        // Group by department if needed
        $groupedData = $payslips->groupBy(function ($payslip) {
            return optional($payslip->user->department)->name ?? 'No Department';
        });

        $reportData = [];
        $totalSalary = 0;
        $totalEmployees = 0;
        $employeeNumber = 1;

        foreach ($groupedData as $departmentName => $departmentPayslips) {
            $departmentTotal = 0;
            $employees = [];

            foreach ($departmentPayslips as $payslip) {
                $employees[] = [
                    'number' => str_pad($employeeNumber, 3, '0', STR_PAD_LEFT) . '-2024',
                    'name' => optional($payslip->user)->full_name ?? 'N/A',
                    'designation' => optional($payslip->user->designation)->name ?? 'N/A',
                    'total_salary' => number_format($payslip->net_salary ?? 0, 2)
                ];

                $departmentTotal += $payslip->net_salary ?? 0;
                $employeeNumber++;
            }

            $reportData[] = [
                'department' => $departmentName,
                'employees' => $employees,
                'department_total' => $departmentTotal
            ];

            $totalSalary += $departmentTotal;
            $totalEmployees += count($employees);
        }

        return [
            'title' => 'ELECTRICAL',
            'subtitle' => 'PAYROLL COVERED AS OF ' . strtoupper(date('M d', strtotime($filters['start_date']))) . '-' . strtoupper(date('M d, Y', strtotime($filters['end_date']))),
            'departments' => $reportData,
            'total_salary' => $totalSalary,
            'total_employees' => $totalEmployees,
            'prepared_by' => $filters['prepared_by'] ?? 'Via Mae N. Tembrevilla',
            'prepared_title' => 'Payroll Analyst',
            'date_range' => [
                'start' => $filters['start_date'],
                'end' => $filters['end_date']
            ]
        ];
    }

    protected function buildTransmittalQuery(array $filters): Builder
    {
        $query = $this->model->query();

        // Date range filter
        if (isset($filters['start_date']) && isset($filters['end_date'])) {
            $query->whereBetween('start_date', [$filters['start_date'], $filters['end_date']])
                  ->orWhereBetween('end_date', [$filters['start_date'], $filters['end_date']]);
        }

        // Department filter
        if (isset($filters['department_id']) && !empty($filters['department_id'])) {
            $query->whereHas('user.department', function ($q) use ($filters) {
                $q->where('id', $filters['department_id']);
            });
        }

        // Payrun filter
        if (isset($filters['payrun_id']) && !empty($filters['payrun_id'])) {
            $query->where('payrun_id', $filters['payrun_id']);
        }

        // Include all payslips (remove status restriction for now)
        // $query->whereHas('status', function ($q) {
        //     $q->whereIn('name', ['sent', 'completed']);
        // });

        return $query->orderBy('created_at', 'desc');
    }
}
